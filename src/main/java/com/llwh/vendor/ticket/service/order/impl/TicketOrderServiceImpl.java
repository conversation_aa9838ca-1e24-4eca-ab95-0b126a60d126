package com.llwh.vendor.ticket.service.order.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.fancylab.api.theatre.common.api.StadiumApiService;
import cn.fancylab.api.theatre.common.req.ChangeOrderCertReq;
import cn.fancylab.api.theatre.common.req.TicketCertDTO;
import cn.fancylab.api.theatre.common.vo.LockInfoVo;
import cn.fancylab.api.theatre.common.vo.OrderRefundVo;
import cn.fancylab.api.theatre.common.vo.ProgramBriefVo;
import cn.fancylab.api.theatre.common.vo.RefundDetailVo;
import cn.fancylab.api.theatre.common.vo.discount.DiscountApportionmentVo;
import cn.fancylab.api.theatre.common.vo.discount.OrderDiscountReq;
import cn.fancylab.api.theatre.common.vo.discount.OrderDiscountVo;
import cn.fancylab.api.theatre.constant.TransportHelper;
import cn.fancylab.api.theatre.refund.vo.RefundReq;
import cn.fancylab.api.theatre.ticket.api.TicketOrderApiService;
import cn.fancylab.api.theatre.ticket.api.TicketSaleApiService;
import cn.fancylab.api.theatre.ticket.vo.SeatDetailVo;
import cn.fancylab.api.theatre.ticket.vo.TicketOrderVo;
import cn.fancylab.api.theatre.ticket.vo.schedule.ScheduleVo;
import cn.fancylab.api.thvendor.member.api.MemberInfoApiService;
import cn.fancylab.api.thvendor.member.vo.MemberPointSpendVo;
import cn.fancylab.api.ucenter.vo.member.MemberVo;
import cn.fancylab.operation.model.LastOperation;
import cn.fancylab.operation.service.MultiOperationService;
import cn.fancylab.pay.client.PayApiClient;
import cn.fancylab.pay.cons.GatewayConst;
import cn.fancylab.pay.cons.GatewayVo;
import cn.fancylab.pay.cons.OrderStatus;
import cn.fancylab.pay.cons.RefundConstant;
import cn.fancylab.rocketmq.MqMessage;
import cn.fancylab.rocketmq.RocketMqProducer;
import cn.fancylab.service.impl.BaseServiceImpl;
import cn.fancylab.support.AuthUser;
import cn.fancylab.support.BizRTException;
import cn.fancylab.support.ResultCode;
import cn.fancylab.support.ResultCodeHelper;
import cn.fancylab.support.Sort;
import cn.fancylab.ticket.Status;
import cn.fancylab.untrans.CacheObjectService;
import cn.fancylab.untrans.CacheService;
import cn.fancylab.util.BeanUtil;
import cn.fancylab.util.DateUtil;
import cn.fancylab.util.IDCardUtil;
import cn.fancylab.util.JsonUtils;
import cn.fancylab.util.ValueUtil;
import cn.fancylab.util.VoCopyUtil;
import cn.fancylab.util.web.Page;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.llwh.api.leaguer.api.LeaguerDubboService;
import com.llwh.base.constant.DiscountCategory;
import com.llwh.base.constant.DiscountStatus;
import com.llwh.base.constant.OrderType;
import com.llwh.vendor.command.ExtraData;
import com.llwh.vendor.constant.JmsConstant;
import com.llwh.vendor.constant.OrderTaskConstant;
import com.llwh.vendor.constant.TaskConstant;
import com.llwh.vendor.general.constant.PaySceneConstant;
import com.llwh.vendor.general.constant.RestrictionRuleTypeConstant;
import com.llwh.vendor.general.helper.BuyPriorityConverter;
import com.llwh.vendor.general.model.RestrictionRule;
import com.llwh.vendor.general.service.GeneralDiscountService;
import com.llwh.vendor.general.service.PaymentMerchantService;
import com.llwh.vendor.general.service.RestrictionRuleService;
import com.llwh.vendor.general.service.bundle.BundleOrderDetailService;
import com.llwh.vendor.general.untrans.refund.RefundProcessorProvider;
import com.llwh.vendor.general.untrans.refund.RefundProcessorRegister;
import com.llwh.vendor.general.vo.PriorityBuyBO;
import com.llwh.vendor.general.vo.RestrictionRuleCheckVo;
import com.llwh.vendor.helper.CheckOwnerUtil;
import com.llwh.vendor.member.model.growth.MemberLevel;
import com.llwh.vendor.member.service.MemberInfoService;
import com.llwh.vendor.member.untrans.MqEventPublisher;
import com.llwh.vendor.model.order.BaseOrder;
import com.llwh.vendor.model.order.OrderDiscount;
import com.llwh.vendor.model.order.OrderRefund;
import com.llwh.vendor.model.order.OrderTask;
import com.llwh.vendor.model.vendor.PaymentGateway;
import com.llwh.vendor.promotion.model.Promotion;
import com.llwh.vendor.promotion.service.PromotionCommonService;
import com.llwh.vendor.service.CompanyService;
import com.llwh.vendor.service.SysParamService;
import com.llwh.vendor.service.order.CommonOrderSuccessedSupportService;
import com.llwh.vendor.service.order.OrderRefundService;
import com.llwh.vendor.service.order.PaymentGatewayService;
import com.llwh.vendor.service.order.TaskService;
import com.llwh.vendor.service.spi.OrderTaskProcessor;
import com.llwh.vendor.service.spi.TaskProcessorRegister;
import com.llwh.vendor.ticket.command.TicketOrderCmd;
import com.llwh.vendor.ticket.model.order.SeatDetail;
import com.llwh.vendor.ticket.model.order.TicketOrder;
import com.llwh.vendor.ticket.service.order.TicketOrderService;
import com.llwh.vendor.ticket.vo.LastLock;
import com.llwh.vendor.ticket.vo.TicketwOrderQueryReq;
import com.llwh.vendor.vo.PayInfoBO;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("ticketOrderService")
public class TicketOrderServiceImpl extends BaseServiceImpl implements TicketOrderService {
	@Autowired
	private TaskService taskService;
	@Autowired
	private CompanyService companyService;
	@Autowired
	private OrderRefundService orderRefundService;
	@Autowired
	private CommonOrderSuccessedSupportService commonOrderSuccessedSupportService;
	@Autowired
	private MultiOperationService multiOperationService;
	@Autowired
	private GeneralDiscountService generalDiscountService;
	@Autowired
	private RestrictionRuleService restrictionRuleService;
	@Autowired
	private RocketMqProducer rocketMqProducer;
	@Autowired
	private MemberInfoService memberInfoService;
	@Autowired
	private PaymentMerchantService paymentMerchantService;
	@Autowired
	private PromotionCommonService promotionCommonService;
	@Autowired
	private CacheObjectService cacheObjectService;
	@Resource
	private BundleOrderDetailService bundleOrderDetailService;

	@DubboReference(version = "1.0")
	private LeaguerDubboService leaguerDubboService;
	@DubboReference(version = "1.0")
	private TicketSaleApiService ticketSaleApiService;
	@DubboReference(version = "1.0")
	private TicketOrderApiService ticketOrderApiService;
	@DubboReference(version = "1.0")
	private StadiumApiService stadiumApiService;
	@Autowired
	private PaymentGatewayService paymentGatewayService;
	@Autowired
	private CacheService cacheService;
	@Autowired
	private PayApiClient payApiClient;
	@Autowired
	private MemberInfoApiService memberInfoApiService;
	@Autowired
	private SysParamService sysParamService;


	@Override
	public ResultCode paidNotifyProcess(String tradeNo, String payseqno, Double paidAmount, String gatewayCode, String merchantCode, String paybank) {
		PayInfoBO payInfo = new PayInfoBO();
		payInfo.setTradeNo(tradeNo);
		payInfo.setPayseqno(payseqno);
		payInfo.setPaidAmount(paidAmount);
		payInfo.setGatewayCode(gatewayCode);
		payInfo.setMerchantCode(merchantCode);
		payInfo.setPaybank(paybank);

		return paidNotifyProcess(payInfo);
	}

	@Override
	public ResultCode paidNotifyProcess(PayInfoBO payInfo) {
		TicketOrder order = getTicketOrder(payInfo.getTradeNo());
		if (order == null) {
			return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("订单不存在！");
		}
		if (OrderStatus.isPaid(order.getPayStatus())) {
			return ResultCode.SUCCESS;
		}
		boolean paidNotCertain = order.getTotalFee() - payInfo.getPaidAmount() > 0;
		// 有优惠 或 支付金额不匹配
		if (order.getDiscount() > 0 || paidNotCertain) {
			order.setPayStatus(OrderStatus.STATUS_PAID_UNCHECK);
		} else {
			order.setPayStatus(OrderStatus.STATUS_PAID_CERTAIN);
		}
		// 加购订单存在且支付金额正确才通知处理
		if (StringUtils.isNotBlank(order.getGoodsTradeNo()) && !paidNotCertain) {
			String goodsTradeNo = order.getGoodsTradeNo();
			OrderTaskProcessor processor = TaskProcessorRegister.getOrderTaskProcessor(OrderType.getOrderType(goodsTradeNo));
			ResultCode code = processor.paidNotifyProcess(goodsTradeNo, payInfo.getPayseqno(), order.getGoodsAmount(), order.getGatewayCode(), order.getMerchantCode(), order.getPaybank());
			if (code.notSuccess()) {
				return ResultCodeHelper.getFailure(code);
			}
		}

		order.setPaidAmount(payInfo.getPaidAmount());
		order.setPayseqno(payInfo.getPayseqno());
		order.setPaidtime(DateUtil.getCurFullTimestamp());
		order.setUpdatetime(order.getPaidtime());
		if (StringUtils.isNotBlank(payInfo.getPromotionStockIds())) {
			Map otherinfo = JsonUtils.readJsonToMap(order.getOtherinfo());
			otherinfo.put("promotionStockIds", payInfo.getPromotionStockIds());
			order.setOtherinfo(JsonUtils.writeObjectToJson(otherinfo));
		}

		baseDao.updateObject(order);
		taskService.addPaidTask(payInfo.getTradeNo(), order.getCompanyId());
		baseDao.addTransactionCompleteCall(() -> {
			Map<String, Object> map = Maps.newHashMap();
			map.put("tradeNo", order.getTradeNo());
			map.put("companyId", order.getCompanyId());
			map.put("memberId", order.getMemberId());
			MqMessage msg = MqMessage.create(map);
			rocketMqProducer.sendMsg(JmsConstant.EVENT_ORDER_TASK, msg);
		});
		return ResultCode.SUCCESS;
	}

	@Override
	public List<OrderTask> processTicketConfirmSucceed(OrderTask task, TicketOrder order) {
		task.setCount(task.getCount() + 1);
		task.setStatus(TaskConstant.STATUS_SUCCESS);
		task.setResult("SUCCESS");
		task.setUpdatetime(DateUtil.getCurFullTimestamp());
		baseDao.updateObject(task);
		List<OrderTask> orderTasks = new ArrayList<>();
		if (order.getOtherFee() > 0) {
			// 其他费用相关处理
			OrderTask otherFee = new OrderTask(task.getTradeNo(), order.getOrderType(), OrderTaskConstant.TYPE_OTHERFEE, task.getRanks() + 10, order.getCompanyId());
			otherFee.setParentId(task.getId());
			baseDao.addObject(otherFee);
			orderTasks.add(otherFee);
		}

		// 场次限购
		OrderTask limitTask = new OrderTask(task.getTradeNo(), order.getOrderType(), OrderTaskConstant.TYPE_SCHEDULE_LIMIT, task.getRanks() + 11, order.getCompanyId());
		limitTask.setParentId(task.getId());
		baseDao.addObject(limitTask);
		orderTasks.add(limitTask);

		Map otherinfo = new HashMap(2);
		otherinfo.put("programId", order.getProgramId());
		otherinfo.put("scheduleId", order.getScheduleId());
		String accessKey = companyService.getAccessKeyByAppkey(order.getAppkey());
		ResultCode<ProgramBriefVo> brief = stadiumApiService.getProgramBriefById(accessKey, order.getProgramId());
		if (brief.isSuccess()) {
			otherinfo.put("programName", brief.getData().getCnName());
		}
		List<OrderTask> tasks = commonOrderSuccessedSupportService.process(task, otherinfo, order);
		if (CollectionUtils.isNotEmpty(tasks)) {
			Collections.sort(tasks, Sort.asc("ranks"));
			orderTasks.addAll(tasks);
		}

		OrderTask returnTask = new OrderTask(task.getTradeNo(), order.getOrderType(), OrderTaskConstant.TYPE_PAID_SUCCESS_MSG, task.getRanks() + 20, order.getCompanyId());
		returnTask.setParentId(task.getId());
		baseDao.addObject(returnTask);
		orderTasks.add(returnTask);
		// unlockCertificate(ovis);
		if (GatewayConst.isDouyin(order.getGatewayCode())) {
			mqEventPublisher.savePushDouyin(order.getTradeNo(), order.getMemberId());
		}

		PriorityBuyBO priorityBuy = BuyPriorityConverter.toPriorityBuyBO(order.getOtherinfo());
		if (priorityBuy != null && StringUtils.isNotBlank(priorityBuy.getPriorityKey())) {
			OrderTask priorityBuyTask = new OrderTask(task.getTradeNo(), order.getOrderType(), OrderTaskConstant.TYPE_PRIORITY_BUY_CONFIRM, task.getRanks() + 30, order.getCompanyId());
			priorityBuyTask.setParentId(task.getId());
			baseDao.addObject(priorityBuyTask);
			orderTasks.add(priorityBuyTask);
		}

		return orderTasks;
	}

	@Autowired
	private MqEventPublisher mqEventPublisher;

	@Override
	public void processOtherFee(OrderTask task) {
		if (StringUtils.equals(task.getType(), OrderTaskConstant.TYPE_OTHERFEE)) {
			task.setCount(task.getCount() + 1);
			task.setStatus(TaskConstant.STATUS_SUCCESS);
			task.setResult("SUCCESS");
			task.setUpdatetime(DateUtil.getCurFullTimestamp());
			baseDao.updateObject(task);
		}
	}

	@Override
	public ResultCode<TicketOrder> cancelOrderByTradeNo(MemberVo member, String tradeNo, boolean byUser) {
		if (paymentGatewayService.isJsPaid(tradeNo, 240)) {
			// 4分钟内前端js告知支付成功不能取消
			throw new BizRTException(ResultCodeHelper.CODE11_DATA_CHECK_ERROR, "订单正在处理中，不能取消！");
		}
		// 查询用户订单
		TicketOrder order = getTicketOrder4Member(tradeNo, member, false);
		if (order == null) {
			return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("订单不存在！");
		}
		return cancelOrderInternal(member, order, byUser);
	}

	@Override
	public LastLock addLastLock(MemberVo member, LockInfoVo lockInfo, String info, String clientIp) {
		LastLock lastLock = new LastLock(lockInfo, info, clientIp);
		cacheService.set(CacheService.REGION_TWENTYMIN, "LastLock" + member.getId(), lastLock);
		return lastLock;
	}

	@Override
	public ResultCode processLastLock(String accessKey, MemberVo member) {
		LastLock last = getLastLock(member);
		if (last != null) {
			TicketOrder order = getTicketOrder4Member(last.getLockNo(), member, false);
			if (order == null) {
				if (last.getOptime() == null || last.getOptime() + 5000 > System.currentTimeMillis()) {
					dbLogger.warn("不能取消时间过短的锁座:{},{},info:{},lockTime:{}", last.getLockNo(), member.getId(), last.getInfo(), last.getLockTime());
					return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("您有一笔锁座正在处理，请稍后重试！");
				}
				// 只有未生成订单时，才自动取消
				ticketSaleApiService.unlockTicketByTradeNo(accessKey, last.getLockNo());
				cleanLastLock(member.getId());
				dbLogger.warn("自动取消上次锁座:{},{},info:{},lockTime:{}", last.getLockNo(), member.getId(), last.getInfo(), last.getLockTime());
			}
		}
		return ResultCode.SUCCESS;
	}

	@Override
	public LastLock getLastLock(MemberVo member) {
		LastLock result = (LastLock) cacheService.get(CacheService.REGION_TWENTYMIN, "LastLock" + member.getId());
		return result;
	}

	@Override
	public void cleanLastLock(Long memberId, String lockNo) {
		String key = "LastLock" + memberId;
		LastLock result = (LastLock) cacheService.get(CacheService.REGION_TWENTYMIN, key);
		if (result != null && StringUtils.equals(result.getLockNo(), lockNo)) {
			dbLogger.warn("下单前自动清理LastLock:{}", lockNo);
			cacheService.remove(CacheService.REGION_TWENTYMIN, key);
		}
	}

	private void cleanLastLock(Long memberId) {
		cacheService.remove(CacheService.REGION_TWENTYMIN, "LastLock" + memberId);
	}

	@Override
	public boolean isUnlocked(String tradeNo) {
		return cacheService.get(CacheService.REGION_HALFHOUR, "unLock" + tradeNo) != null;
	}

	@Override
	public ResultCode cancelOrUnlockTicket(String accessKey, MemberVo member, String lockNo, boolean byUser) {
		TicketOrder order = getTicketOrder4Member(lockNo, member, false);
		if (order != null) {
			if (paymentGatewayService.isJsPaid(order.getTradeNo(), 240)) {
				// 4分钟内前端js告知支付成功不能取消
				throw new BizRTException(ResultCodeHelper.CODE11_DATA_CHECK_ERROR, "订单正在处理中，不能取消！");
			}
			return cancelOrderInternal(member, order, byUser);
		}
		// 防止在解锁的同时，正在创建订单
		cacheService.set(CacheService.REGION_HALFHOUR, "unLock" + lockNo, "Y");
		ResultCode code = ticketSaleApiService.unlockTicketByTradeNo(accessKey, lockNo);
		LastLock last = getLastLock(member);
		if (last != null && StringUtils.equals(lockNo, last.getLockNo())) {
			cleanLastLock(member.getId());
			dbLogger.warn("清理lastLock:{},{}, info:{},lockTime:{}", lockNo, member.getId(), last.getInfo(), last.getLockTime());
		}
		return code;
	}

	private ResultCode<TicketOrder> cancelOrderInternal(MemberVo member, TicketOrder order, boolean byUser) {
		String realStatus = order.getRealStatus();
		// 订单状态检查
		if (OrderStatus.isCancel(realStatus)) {
			return ResultCode.getSuccessReturn(order);
		}

		if (OrderStatus.isPaid(order.getPayStatus())) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("订单已支付，不可取消！");
		}

		if (OrderStatus.isNew(realStatus)) {
			if (OrderStatus.isNewConfirm(realStatus)) {
				ResultCode payCode = payApiClient.closePayOrder(order.getCompanyId(), order.getTradeNo());
				if (payCode.notSuccess()) {
					if (order.getUpdatetime().getTime() + 5 * DateUtil.m_minute > System.currentTimeMillis()) {
						return ResultCodeHelper.getFailure(payCode);
					}
				}
				//if (isPaying(order.getTradeNo()) || order.getUpdatetime().getTime() + DateUtil.m_minute > System.currentTimeMillis()) {
				//	return ResultCodeHelper.CODE10_PROHIBITED("待支付订单1分钟内不可取消！");
				//}
			}
			String accessKey = companyService.getAccessKeyByAppkey(order.getAppkey());
			ResultCode code = ticketSaleApiService.unlockTicketByTradeNo(accessKey, order.getTradeNo());

			if (!code.isSuccess()) {
				return ResultCodeHelper.getFailure(code);
			}

			List<SeatDetail> items = getOrderItemsByTradeNo(order.getTradeNo());
			order.setOrderItems(items);
			cancelDiscount(order);
			Timestamp validtime = new Timestamp(System.currentTimeMillis() - 1000);
			Timestamp cur = DateUtil.getCurFullTimestamp();
			String cancelStatus = byUser ? OrderStatus.STATUS_CANCEL_USER : OrderStatus.STATUS_CANCEL_SYS;
			order.setPayStatus(cancelStatus);
			order.setValidtime(validtime);
			order.setUpdatetime(cur);
			long mill = cur.getTime() - order.getAddtime().getTime();
			order.setCancelSecond((int) mill / 1000);
			baseDao.saveObject(order);
			dbLogger.warn("取消未支付订单：{}", order.getTradeNo());

			// 组合订单取消组合订单明细
			if (order.isBundleOrder()) {
				bundleOrderDetailService.cancelBundleOrderDetail(order.getPackTradeNo(), order);
			}
			return ResultCode.getSuccessReturn(order);
		} else {
			// 不可能错误
			dbLogger.warn("取消未支付订单，订单状态错误：{}: {}", order.getTradeNo(), realStatus);
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("订单状态错误！");
		}
	}


	@Override
	public ResultCode<TicketOrder> cancelOrderByTbs(AuthUser user, String tradeNo, boolean unclosePay) {
		TicketOrder order = getTicketOrder(tradeNo);
		if (order == null) {
			dbLogger.error("订单不存在tradeNo:{}", tradeNo);
			return ResultCode.SUCCESS;
		}
		CheckOwnerUtil.checkCompanyOwner(user, order);
		String realStatus = order.getRealStatus();
		// 订单状态检查
		if (OrderStatus.isCancel(realStatus)) {
			return ResultCode.getSuccessReturn(order);
		}

		if (OrderStatus.isPaid(order.getPayStatus())) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("订单已支付，不可取消！");
		}

		if (OrderStatus.isNew(realStatus)) {
			if (!unclosePay) {
				ResultCode payCode = payApiClient.closePayOrder(order.getCompanyId(), order.getTradeNo());
				if (payCode.notSuccess()) {
					return ResultCodeHelper.getFailure(payCode);
				}
			}
			String accessKey = companyService.getAccessKeyByAppkey(order.getAppkey());
			ResultCode code = ticketSaleApiService.unlockTicketByTradeNo(accessKey, order.getTradeNo());
			if (!code.isSuccess()) {
				return ResultCodeHelper.getFailure(code);
			}

			List<SeatDetail> items = getOrderItemsByTradeNo(order.getTradeNo());
			order.setOrderItems(items);
			cancelDiscount(order);
			Timestamp validtime = new Timestamp(System.currentTimeMillis() - 1000);
			Timestamp cur = DateUtil.getCurFullTimestamp();
			order.setPayStatus(OrderStatus.STATUS_CANCEL_SYS);
			order.setValidtime(validtime);
			order.setUpdatetime(cur);
			long mill = cur.getTime() - order.getAddtime().getTime();
			order.setCancelSecond((int) mill / 1000);
			baseDao.saveObject(order);
			dbLogger.warn("取消未支付订单：{}", order.getTradeNo());

			// 组合订单取消组合订单明细
			if (order.isBundleOrder()) {
				bundleOrderDetailService.cancelBundleOrderDetail(order.getPackTradeNo(), order);
			}
			return ResultCode.getSuccessReturn(order);
		} else {
			// 不可能错误
			dbLogger.warn("取消未支付订单，订单状态错误：{}: {}", order.getTradeNo(), realStatus);
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("订单状态错误！");
		}
	}

	private void setPaying(String tradeNo) {
		String key = "Pay" + tradeNo;
		cacheService.set(CacheService.REGION_ONEMIN, key, System.currentTimeMillis());
	}
	/*
	private boolean isPaying(String tradeNo) {
		String key = "Pay" + tradeNo;
		Object pay = cacheService.get(CacheService.REGION_ONEMIN, key);
		return pay != null;
	}*/

	private void cancelDiscount(TicketOrder order) {
		List<OrderDiscount> discountList = baseDao.getObjectListByField(OrderDiscount.class, "tradeNo", order.getTradeNo());
		boolean hasPromotion = false;
		for (OrderDiscount discount : discountList) {
			if (DiscountCategory.isCoupon(discount.getCategory())) {
				// 1) 券取消
				leaguerDubboService.unlockCouponByCardno(discount.getCardNo(), order.getTradeNo());
			} else if (DiscountCategory.isPromotion(discount.getCategory())) {
				// 2) 处理优惠活动的使用记录、名额
				hasPromotion = true;
			} else if (DiscountCategory.isMemership(discount.getCategory())) {
				// 3) 处理会员权益、会员卡的使用记录
				// TODO: 处理
			} else if (DiscountCategory.isPoints(discount.getCategory())) {
				// 4) 处理积分
				// ignore, 不需要处理
			} else if (DiscountCategory.isSamePrice(discount.getCategory())) {
				// 5) 不处理，套票在theatre中取消
			}
		}
		if (hasPromotion) {
			promotionCommonService.cancelOrderAndReturnStock(order.getTradeNo());
		}
	}

	private static String lastTicketKey(Long companyId, Long memberId) {
		return "ticket" + memberId + "_" + companyId;
	}

	@Override
	public TicketOrder getLastTicketOrder(Long companyId, Long memberId) {
		LastOperation lastOperation = baseDao.getObject(LastOperation.class, lastTicketKey(companyId, memberId));
		if (lastOperation != null) {
			TicketOrder order = getTicketOrder(lastOperation.getLastvalue());
			if (order != null && order.getCompanyId().equals(companyId)) {
				return order;
			}
		}
		return null;
	}

	@Override
	public TicketOrder getTicketOrder(String tradeNo) {
		return baseDao.getObjectByUkey(TicketOrder.class, "tradeNo", tradeNo);
	}

	@Override
	public TicketOrder getTicketOrder4Member(String tradeNo, MemberVo member, boolean withDetails) {
		Assert.isTrue(StringUtils.isNotBlank(tradeNo), "tradeNo is need");
		Assert.notNull(member, "member is need");
		TicketOrder order = baseDao.getObjectByUkey(TicketOrder.class, "tradeNo", tradeNo);
		if (order == null) {
			return null;
		}
		if (!order.getMemberId().equals(member.getId())) {
			dbLogger.error("非法查询订单：{}，{}", tradeNo, member.getId());
			return null;
		}
		if (withDetails) {
			List<SeatDetail> details = getOrderItemsByTradeNo(tradeNo);
			order.setOrderItems(details);
		}
		return order;
	}

	@Override
	public List<SeatDetail> getOrderItemsByTradeNo(String tradeNo) {
		Assert.isTrue(StringUtils.isNotBlank(tradeNo), "tradeNo is need");
		return baseDao.getObjectListByField(SeatDetail.class, "tradeNo", tradeNo);
	}

	@Override
	public List<TicketOrder> queryTicketOrders(Long companyId, Long memberId, TicketwOrderQueryReq req, Page page) {
		DetachedCriteria query = DetachedCriteria.forClass(TicketOrder.class);
		query.add(Restrictions.eq("companyId", companyId));
		query.add(Restrictions.eq("memberId", memberId));
		query.add(Restrictions.isEmpty("packTradeNo"));
		if (StringUtils.equals(req.getStatus(), "paid")) {
			// 已支付
			query.add(Restrictions.like("payStatus", "paid%"));
		} else if (StringUtils.equals(req.getStatus(), "new")) {
			// 待支付
			query.add(Restrictions.like("payStatus", "new%"));
			query.add(Restrictions.gt("validtime", DateUtil.getCurFullTimestamp()));
		} else if (StringUtils.isNotBlank(req.getStatus())) {
			query.add(Restrictions.like("payStatus", req.getStatus()));
		}

		if (CollectionUtils.isNotEmpty(req.getProgramIds())) {
			query.add(Restrictions.in("programId", req.getProgramIds()));
		}
		List<TicketOrder> orderList = new ArrayList();
		if (page != null) {
			int total = baseDao.getObjectCountByCriteria(query);
			page.setTotal(total);
			if (total > 0) {
				query.setProjection(null);
				query.addOrder(Order.desc("updatetime"));
				orderList = baseDao.findByCriteria(query, page.getStartRow(), page.getPageSize());
			}
		} else {
			query.addOrder(Order.desc("updatetime"));
			orderList = baseDao.findByCriteria(query);
		}
		return orderList;
	}

	@Override
	public OrderTask proccessPaidCheckSucced(OrderTask task) {
		task.setCount(task.getCount() + 1);
		task.setStatus(TaskConstant.STATUS_SUCCESS);
		task.setResult("SUCCESS");
		task.setUpdatetime(DateUtil.getCurFullTimestamp());
		baseDao.updateObject(task);
		OrderTask returnTask = new OrderTask(task.getTradeNo(), OrderType.getOrderType(task.getTradeNo()), OrderTaskConstant.TYPE_TICKET_CONFIRM, task.getRanks() + 10, task.getCompanyId());
		returnTask.setParentId(task.getId());
		baseDao.addObject(returnTask);
		return returnTask;
	}

	@Override
	public ResultCode<TicketOrder> updatePayment(String tradeNo, MemberVo member, PaymentGateway gateway, String remark) {
		TicketOrder order = getTicketOrder4Member(tradeNo, member, false);
		if (order == null) {
			return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("订单不存在！");
		}
		if (!StringUtils.equals(order.getAppkey(), member.getAppkey())) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("非本渠道订单，请到原渠道支付");
		}
		if (!OrderStatus.isNew(order.getRealStatus())) {
			return ResultCodeHelper.CODE11_PARAMS_REQUIRED("非待支付订单！");
		}
		if (GatewayConst.isWeixin(gateway.getGatewayCode())) {
			order.setOpenid(member.getOpenid());
			order.setAppid(member.getAppid());
		}
		order.setPayStatus(OrderStatus.STATUS_NEW_CONFIRMED);
		Map addinfo = JsonUtils.readJsonToMap(order.getAdditionInfo());
		addinfo.put("remark", remark);

		order.setGatewayCode(gateway.getGatewayCode());
		order.setPaybank(gateway.getPaybank());
		order.setMerchantCode(gateway.getMerchantCode());
		if (!StringUtils.contains(gateway.getFlag(), "fix")) {// fix表示不允许变更
			GatewayVo specified = paymentMerchantService.getSpecifiedGateway(order.getCompanyId(), PaySceneConstant.PAY_SCENE_PROGRAM, order.getProgramId(), gateway);
			if (specified == null) {
				specified = paymentMerchantService.getSpecifiedGateway(order.getCompanyId(), PaySceneConstant.PAY_SCENE_STADIUM, order.getStadiumId(), gateway);
			}
			if (specified == null) {
				specified = paymentMerchantService.getAgencySpecifiedGatewayByOtherRelated(order.getCompanyId(), PaySceneConstant.PAY_SCENE_ORDER_TYPE, order.getOrderType(), null, gateway);
			}
			if (specified != null) {
				order.setGatewayCode(specified.getGatewayCode());
				order.setMerchantCode(specified.getMerchantCode());
				order.setPaybank(specified.getPaybank());
				if (StringUtils.isNotBlank(specified.getOtherMerchantCode())) {
					addinfo.put("otherMerchantCode", specified.getOtherMerchantCode());
				}
			}
		}
		order.setAdditionInfo(JsonUtils.writeMapToJson(addinfo));
		baseDao.addTransactionCompleteCall(() -> {
			// 标记为支付中，1分钟内不能取消
			setPaying(tradeNo);
		});
		baseDao.saveObject(order);
		return ResultCode.getSuccessReturn(order);
	}

	@Override
	public ResultCode<TicketOrder> createTicketOrder(MemberVo member, TicketOrderCmd orderCmd2, TicketOrderVo remoteOrder, PriorityBuyBO priorityBuy) throws BizRTException {
		TicketOrder order = createFromRemoteOrderInternal(member, remoteOrder);
		order.setPackTradeNo(orderCmd2.getPackTradeNo());
		order.setAppid(orderCmd2.getAppId());

		ExtraData extra = orderCmd2.getExtra();
		if (extra != null) {
			order.setOrigin(extra.getOrigin());
			order.setTraceInfo(extra.getTraceInfo());
		}
		if(priorityBuy != null){
			Map<String, Object> otherinfo = new HashMap<>();
			otherinfo.put(BuyPriorityConverter.OTHERINFO_KEY_PRIORITY_BUY, JsonUtils.writeObjectToJson(priorityBuy));
			order.setOtherinfo(JsonUtils.writeMapToJson(otherinfo));
		}
		baseDao.addObject(order);
		baseDao.addObjectList(order.getOrderItems());
		for (SeatDetail orderItem : order.getOrderItems()) {
			baseDao.saveObject(orderItem);
		}

		multiOperationService.updateLastOperation(lastTicketKey(order.getCompanyId(), order.getMemberId()), order.getTradeNo(), order.getAddtime(), order.getValidtime(), order.getOrderType());
		return ResultCode.getSuccessReturn(order);

	}
	@Override
	public ResultCode<TicketOrder> createFromRemoteOrder(MemberVo member, TicketOrderVo remoteOrder, List<OrderDiscountVo> discountList) throws BizRTException {
		TicketOrder order = createFromRemoteOrderInternal(member, remoteOrder);
		baseDao.addObject(order);
		baseDao.addObjectList(order.getOrderItems());
		for (SeatDetail orderItem : order.getOrderItems()) {
			baseDao.saveObject(orderItem);
		}

		if (CollectionUtils.isNotEmpty(discountList)) {
			List<OrderDiscount> discounts = new ArrayList<>();
			Timestamp cur = new Timestamp(System.currentTimeMillis());
			for(OrderDiscountVo dvo: discountList) {
				OrderDiscount discount = new OrderDiscount(order, cur);
				VoCopyUtil.copyFromObj(discount, dvo);
				discount.setId(null);
				discount.setMemberId(member.getId());
				// 不需要再次从thvendor同步到theatre
				discount.setStatus(DiscountStatus.CONFIRM_SYNCH);
				baseDao.saveObject(discount);
				discounts.add(discount);
			}
			order.setDiscountList(discounts);
		}

		multiOperationService.updateLastOperation(lastTicketKey(order.getCompanyId(), order.getMemberId()), order.getTradeNo(), order.getAddtime(), order.getValidtime(), order.getOrderType());
		return ResultCode.getSuccessReturn(order);
	}

	/**
	 * @param member
	 * @param orderVo
	 * @return
	 * @throws BizRTException
	 */
	private TicketOrder createFromRemoteOrderInternal(MemberVo member, TicketOrderVo orderVo) throws BizRTException {
		TicketOrder order = new TicketOrder(member.getAppkey(), orderVo.getTradeNo());
		order.setClientIp(orderVo.getClientIp());
		order.setOrderType(OrderType.TYPE_TICKET);
		order.setMemberId(member.getId());
		order.setMemberName(member.getNickname());
		order.setMobile(member.getMobile());
		order.setContactMobile(orderVo.getContactMobile());
		order.setAppkey(member.getAppkey());
		order.setOpenid(member.getOpenid());
		order.setAppid(member.getAppid());
		// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
		order.setCompanyId(member.getCompanyId());
		order.setPlatform(orderVo.getPlatform());
		order.setScheduleId(orderVo.getScheduleId());
		order.setStadiumId(orderVo.getStadiumId());
		order.setProgramId(orderVo.getProgramId());
		order.setPlayTime(orderVo.getPlayTime());
		order.setAmount(orderVo.getSeatAmount());
		order.setOtherFee(orderVo.getOtherfee());
		order.setDiscount(orderVo.getDiscount());
		order.setTotalFee(orderVo.getFee());
		order.setPayStatus(orderVo.getPayStatus());
		// 以远程的validtime为准
		order.setValidtime(orderVo.getValidtime());
		order.setTitle(orderVo.getTitle());
		order.setContactName(orderVo.getContactName());
		order.setCertificateNo(orderVo.getCertificateNo());
		order.setCertificateType(orderVo.getCertificateType());

		List<SeatDetail> seats = new ArrayList<>();

		Timestamp cur = DateUtil.getCurFullTimestamp();

		for (SeatDetailVo detail : orderVo.getDetails()) {
			SeatDetail seatDetail = createOrderItem(order, detail, cur);

			seats.add(seatDetail);
		}

		order.setOrderItems(seats);
		order.setQuantity(seats.size());
		order.setOriTradeNo(orderVo.getOriTradeNo());
		order.setChangeFlag(orderVo.getChangeFlag());
		order.setExpressAddress(orderVo.getExpressAddress());
		order.setTransport(orderVo.getTransport());
		return order;
	}

	private SeatDetail createOrderItem(TicketOrder order, SeatDetailVo detail, Timestamp cur) throws BizRTException {
		SeatDetail seatDetail = new SeatDetail(order.getCompanyId(), order.getTradeNo());
		// 直接等于父订单类型
		seatDetail.setScheduleId(order.getScheduleId());
		seatDetail.setOrderType(order.getOrderType());
		seatDetail.setMemberId(order.getMemberId());
		seatDetail.setCompanyId(order.getCompanyId());
		seatDetail.setMemberId(order.getMemberId());
		seatDetail.setColNo(detail.getColNo());
		seatDetail.setRowNo(detail.getRowNo());
		seatDetail.setVenueAreaId(detail.getVenueAreaId());
		seatDetail.setVenueAreaName(detail.getVenueAreaName());
		seatDetail.setUuid(detail.getUuid());
		seatDetail.setRealname(detail.getRealname());
		seatDetail.setCertificateNo(detail.getCertificateNo());
		seatDetail.setCertificateType(detail.getCertificateType());
		seatDetail.setSpecialFlag(detail.getSpecialFlag());
		seatDetail.setAmount(detail.getTicketPrice());
		seatDetail.setDiscount(detail.getDiscount());
		seatDetail.setTicketPriceId(detail.getTicketPriceId());
		seatDetail.setUnitPrice(detail.getTicketPrice());
		return seatDetail;
	}

	@Override
	public ResultCode processLastTicketOrder(Long companyId, Long memberId) {
		TicketOrder lastOrder = getLastTicketOrder(companyId, memberId);
		if (lastOrder == null) {
			return ResultCode.SUCCESS;
		}
		double diffMin = DateUtil.getDiffMinu(DateUtil.getCurFullTimestamp(), lastOrder.getAddtime());
		if (StringUtils.equalsAny(lastOrder.getPayStatus(), OrderStatus.STATUS_PAID_CERTAIN, OrderStatus.STATUS_PAID_UNCHECK) && diffMin < 20) {
			return ResultCodeHelper.CODE11_DATA_EXISTS("您还有一个订单等待处理，订单号为" + lastOrder.getTradeNo() + "，请稍后再下新订单！");
		}
		if (lastOrder.isNew()) {
			if (lastOrder.isBundleOrder()) {
				return ResultCodeHelper.getFullCode(ResultCodeHelper.CODE11_DATA_EXISTS, "您还有一个订单[" + lastOrder.getPackTradeNo() + "]未处理，请到订单中心去取消或支付!", lastOrder.getPackTradeNo());
			} else {
				return ResultCodeHelper.getFullCode(ResultCodeHelper.CODE11_DATA_EXISTS, "您还有一个订单[" + lastOrder.getTradeNo() + "]未处理，请到订单中心去取消或支付!", lastOrder.getTradeNo());
			}
		}
		return ResultCode.SUCCESS;
	}

	@Override
	public ResultCode saveRemoteOrder(TicketOrderVo ticketOrderVo, TicketOrder order, boolean changeSeat) throws BizRTException {
		Timestamp cur = DateUtil.getCurFullTimestamp();
		order.setPayStatus(ticketOrderVo.getPayStatus());
		order.setUpdatetime(cur);
		order.setPushflag(Status.STATUS_Y);
		order.setOrderQrcode(ticketOrderVo.getOrderQrcode());
		order.setValidCode(ticketOrderVo.getValidCode());
		List<SeatDetail> details = getOrderItemsByTradeNo(order.getTradeNo());
		List<SeatDetailVo> detailVos = ticketOrderVo.getDetails();
		Map<String, SeatDetailVo> voMap = BeanUtil.beanListToMap(detailVos, "uuid");
		for (SeatDetail detail : details) {
			SeatDetailVo vo = voMap.get(detail.getUuid());
			if (changeSeat) {
				// 座位是通过预留换过, 重新设置
				detail.setColNo(vo.getColNo());
				detail.setRowNo(vo.getRowNo());
				detail.setVenueAreaId(vo.getVenueAreaId());
				detail.setVenueAreaName(vo.getVenueAreaName());
			}
			detail.setDiscount(vo.getDiscount());
		}
		baseDao.updateObjectList(details);
		baseDao.updateObject(order);
		return ResultCode.SUCCESS;
	}

	@Override
	public ResultCode processPaidDiscount(TicketOrder order) {
		if (OrderStatus.isPaidCertain(order.getPayStatus())) {
			return ResultCode.SUCCESS;
		}
		if (!OrderStatus.isPaidUncheck(order.getPayStatus())) {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("订单状态不正确！");
		}
		List<SeatDetail> details = order.getOrderItems();
		if (CollectionUtils.isEmpty(details)) {
			details = getOrderItemsByTradeNo(order.getTradeNo());
			order.setOrderItems(details);
		}
		String level1Ukey = "" + order.getProgramId();
		String level2Ukey = "" + order.getScheduleId();
		return generalDiscountService.processPaidDiscount(order, level1Ukey, level2Ukey);
	}

	@Override
	public ResultCode createRemoteDiscount(TicketOrder order) {
		List<OrderDiscount> discountList = order.getDiscountList();
		if (discountList == null) {
			discountList = baseDao.getObjectListByField(OrderDiscount.class, "tradeNo", order.getTradeNo());
			order.setDiscountList(discountList);
		}
		if (CollectionUtils.isEmpty(discountList)) {
			return ResultCode.SUCCESS;
		}

		if (DiscountStatus.isConfirmSynch(discountList.get(0).getStatus())) {
			return ResultCode.SUCCESS;
		}
		List<SeatDetail> seats = order.getOrderItems();
		if (seats == null) {
			seats = baseDao.getObjectListByField(SeatDetail.class, "tradeNo", order.getTradeNo());
			order.setOrderItems(seats);
		}
		Map<Long, SeatDetail> seatMap = BeanUtil.beanListToMap(seats, "id");

		OrderDiscountReq req = new OrderDiscountReq(order.getTradeNo(), order.getDiscount());
		List<OrderDiscountVo> discountVoList = new ArrayList<>();
		for (OrderDiscount discount : discountList) {
			List<String> uuids = new ArrayList<>();
			for (Long seatId : BeanUtil.getIdList(discount.getUsedItems(), ",")) {
				uuids.add(seatMap.get(seatId).getUuid());
			}
			OrderDiscountVo vo = new OrderDiscountVo();
			VoCopyUtil.copyFromObj(vo, discount);
			vo.setUuids(StringUtils.join(uuids, ","));
			discountVoList.add(vo);
		}
		req.setDiscountList(discountVoList);
		String accessKey = companyService.getAccessKeyByAppkey(order.getAppkey());
		ResultCode<TicketOrderVo> createResult = ticketSaleApiService.createTicketDiscount(accessKey, req);
		if (createResult.isSuccess()) {
			// 1)同步处理状态
			for (OrderDiscount discount : discountList) {
				discount.setStatus(DiscountStatus.CONFIRM_SYNCH);
			}
			baseDao.saveObjectList(discountList);
			// 2）同步座位折扣
			Map<String, SeatDetail> seatUuidMap = BeanUtil.beanListToMap(seats, "uuid");
			for (SeatDetailVo seatVo : createResult.getData().getDetails()) {
				if (seatVo.getDiscount() >= 0.01) {
					SeatDetail seatDetail = seatUuidMap.get(seatVo.getUuid());
					seatDetail.setDiscount(seatVo.getDiscount());
					baseDao.saveObject(seatDetail);
				}
			}
			return ResultCode.SUCCESS;
		}
		return createResult;
	}

	@Override
	public void processTicketOrderRefundSuccess(TicketOrder order, OrderRefundVo refundVo) {
		Assert.notNull(order, "order is need");
		if (OrderStatus.isPaidReturn(order.getPayStatus())) {
			return;
		}
		String tradeNo = order.getTradeNo();
		String accessKey = companyService.getAccessKeyByAppkey(order.getAppkey());
		ResultCode<List<SeatDetailVo>> seatDetails = ticketOrderApiService.getSeatDetails(accessKey, tradeNo);

		Map<String, SeatDetailVo> detailMap = BeanUtil.beanListToMap(seatDetails.getData(), "uuid");
		List<SeatDetail> items = getOrderItemsByTradeNo(tradeNo);
		Timestamp cur = DateUtil.getCurFullTimestamp();
		// refund 中的冗余信息以处理 items 中哪些是应该被调整的，当都退票成功后，整单调整
		boolean allRefundSuccess = true;
		for (SeatDetail item : items) {
			SeatDetailVo detail = detailMap.get(item.getUuid());
			if (detail == null) {
				dbLogger.warn("detail is null, {}, {}, {}", refundVo.getSerialNo(), tradeNo, item.getUuid());
				allRefundSuccess = false;
				continue;
			}
			if (!OrderStatus.isPaidReturn(detail.getPayStatus())) {
				allRefundSuccess = false;
				continue;
			}
			item.setUpdatetime(cur);
			baseDao.updateObject(item);
		}
		if (allRefundSuccess) {
			dbLogger.warn("allRefundSuccess:{}", order.getTradeNo());
			order.setPayStatus(OrderStatus.STATUS_PAID_RETURN_SUCC);
			order.setUpdatetime(cur);
			baseDao.updateObject(order);
			


			processDiscountRefund(order, refundVo, items, detailMap);
		} else {
			List<String> refundUuids = seatDetails.getData().stream().filter(d -> OrderStatus.isPaidReturn(d.getPayStatus())).map(SeatDetailVo::getUuid).collect(Collectors.toList());
			List<Long> refundDetailIds = items.stream().filter(d -> refundUuids.contains(d.getUuid())).map(SeatDetail::getId).distinct().collect(Collectors.toList());
			List<OrderDiscount> discountList = baseDao.getObjectListByField(OrderDiscount.class, "tradeNo", order.getTradeNo());
			for (OrderDiscount discount : discountList) {
				if (DiscountStatus.isRefund(discount.getStatus())) {
					continue;
				}
				List<Long> usedItemIds = getUsedItemIds(discount.getUsedItems());
				if (CollectionUtils.isEmpty(usedItemIds)) {
					continue;
				}

				if (DiscountCategory.isCoupon(discount.getCategory()) || DiscountCategory.isMultiCard(discount.getCategory())) {
					if (!CollectionUtils.isSubCollection(usedItemIds, refundDetailIds)) {
						dbLogger.warn("SerialNo:{}, usedItemIds:{}, refundDetailIds:{}", refundVo.getSerialNo(), usedItemIds, refundDetailIds);
						continue;
					}
					leaguerDubboService.refundCouponByCardNo(discount.getCardNo(), order.getTradeNo());
					discount.setStatus(DiscountStatus.REFUND);
					baseDao.saveObject(discount);
				} else if (DiscountCategory.isPoints(discount.getCategory())) {
					refundPoints(discount, refundVo, usedItemIds, items, detailMap);
				}
			}
		}

//		String orderLimitKey = OrderLimitHelper.getOrderLimitKey(OrderType.TYPE_TICKET, order.getScheduleId(), order.getMemberId());
//		operationService.restoreOperationNum(orderLimitKey, refundVo.getQuantity());
	}

	private void refundPoints(OrderDiscount discount, OrderRefundVo refundVo, List<Long> usedItemIds, List<SeatDetail> seatDetails, Map<String, SeatDetailVo> detailMap) {
		if (!DiscountCategory.isPoints(discount.getCategory())) {
			return;
		}
		List<String> curRefundUuids = refundVo.getDetails().stream().map(RefundDetailVo::getUuid).collect(Collectors.toList());
		List<Long> curRefundDetailIds = seatDetails.stream().filter(d -> curRefundUuids.contains(d.getUuid())).map(SeatDetail::getId).distinct().collect(Collectors.toList());
		if (!CollectionUtils.containsAny(curRefundDetailIds, usedItemIds)) {
			return;
		}

		ResultCode<List<DiscountApportionmentVo>> resultCode = ticketOrderApiService.getDiscountApportionment(discount.getCompanyId(), Lists.newArrayList(discount.getTradeNo()));
		if (resultCode.notSuccess() || CollectionUtils.isEmpty(resultCode.getData())) {
			dbLogger.error("getDiscountApportionment error or Empty, resultCode:{}, TradeNo:{}, serialNo:{}", JsonUtils.writeObjectToJson(resultCode), discount.getTradeNo(), refundVo.getOutSerialNo());
			return;
		}
		List<DiscountApportionmentVo> usePointTickets = resultCode.getData().stream().filter(d -> DiscountCategory.isPoints(d.getCategory())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(usePointTickets)) {
			dbLogger.error("usePointTickets is Empty, TradeNo:{}, serialNo:{}", discount.getTradeNo(), refundVo.getOutSerialNo());
			return;
		}

		double discountAmount = usePointTickets.stream().filter(t -> curRefundUuids.contains(t.getUuid())).mapToDouble(DiscountApportionmentVo::getDiscountAmount).sum();
		if (ValueUtil.moneyLte(discountAmount, 0d)) {
			dbLogger.warn("discountAmount is 0, TradeNo:{}, discountAmount:{}, serialNo:{}", discount.getTradeNo(), discountAmount, refundVo.getOutSerialNo());
			return;
		}
		int pointDeductionRatio = sysParamService.getPointDeductionRatio(discount.getCompanyId());
		Integer pointValue = ValueUtil.roundDouble(discountAmount * pointDeductionRatio).intValue();
		ResultCode pointReturnResultCode = memberPointReturn(discount.getMemberId(), discount, refundVo.getSerialNo(), pointValue);
		if (pointReturnResultCode.notSuccess()) {
			dbLogger.error("memberPointReturn faild, tradeNo:{}, orderDiscountId:{}, pointReturnResultCode:{}, pointValue:{}, serialNo:{}", discount.getTradeNo(), discount.getId(), JsonUtils.writeObjectToJson(pointReturnResultCode), pointValue, refundVo.getOutSerialNo());
			return;
		}

		List<String> usedUuids = seatDetails.stream().filter(d -> usedItemIds.contains(d.getId())).map(SeatDetail::getUuid).distinct().collect(Collectors.toList());
		boolean allRefundSuccess = true;
		for (String uuid : usedUuids) {
			SeatDetailVo detail = detailMap.get(uuid);
			if (detail == null) {
				dbLogger.warn("detail is null, {}, {}, {}", refundVo.getSerialNo(), discount.getTradeNo(), uuid);
				allRefundSuccess = false;
				break;
			}
			if (!OrderStatus.isPaidReturn(detail.getPayStatus())) {
				allRefundSuccess = false;
				break;
			}
		}
		if (allRefundSuccess) {
			dbLogger.warn("detail allRefundSuccess, {}, {}, {}", refundVo.getSerialNo(), discount.getTradeNo(), usedUuids);
			discount.setStatus(DiscountStatus.REFUND);
			baseDao.saveObject(discount);
		}
	}

	private List<Long> getUsedItemIds(String usedItems) {
		if (StringUtils.isBlank(usedItems)) {
			return new ArrayList<>();
		}
		List<String> items = Lists.newArrayList(usedItems.split(","));

		List<Long> ids = new ArrayList<>();
		for (String item : items) {
			if (item.contains(":")) {
				String[] arr = item.split(":");
				ids.add(Long.valueOf(arr[0]));
			} else {
				ids.add(Long.valueOf(item));
			}
		}
		return ids.stream().distinct().collect(Collectors.toList());
	}

	@Override
	public ResultCode<List<OrderDiscount>> processDiscountRefund(AuthUser user, String tradeNo) {
		TicketOrder order = getTicketOrder(tradeNo);
		if (order == null) {
			return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("订单不存在：" + tradeNo);
		}
		CheckOwnerUtil.checkCompanyOwner(user, order);
		if (OrderStatus.STATUS_PAID_RETURN_SUCC.equals(order.getPayStatus())) {
			List<OrderDiscount> result = processDiscountRefund(order, null, null, null);
			return ResultCode.getSuccessReturn(result);
		} else {
			return ResultCodeHelper.CODE11_DATA_CHECK_ERROR("订单非全部退款：" + tradeNo);
		}
	}

	private List<OrderDiscount> processDiscountRefund(TicketOrder order, OrderRefundVo refundVo, List<SeatDetail> seatDetails, Map<String, SeatDetailVo> detailMap) {
		List<OrderDiscount> discountList = baseDao.getObjectListByField(OrderDiscount.class, "tradeNo", order.getTradeNo());
		List<OrderDiscount> result = new ArrayList<>();
		for (OrderDiscount discount : discountList) {
			if (DiscountStatus.isRefund(discount.getStatus())) {
				continue;
			}
			if (DiscountCategory.isCoupon(discount.getCategory()) || DiscountCategory.isMultiCard(discount.getCategory())) {
				leaguerDubboService.refundCouponByCardNo(discount.getCardNo(), order.getTradeNo());
				discount.setStatus(DiscountStatus.REFUND);
				baseDao.saveObject(discount);
			} else if (DiscountCategory.isMemership(discount.getCategory()) || DiscountCategory.isPromotion(discount.getCategory())) {
				String level1Ukey = "" + order.getProgramId();
				String level2Ukey = "" + order.getScheduleId();
				Promotion promotion = cacheObjectService.getObject(Promotion.class, discount.getCategoryId());
				if (promotion != null) {
					promotionCommonService.resetMemberAllow(promotion, level1Ukey, level2Ukey, order.getMemberId(), discount.getDetailCount());
					promotionCommonService.refundDiscount(promotion, discount);
				}
				discount.setStatus(DiscountStatus.REFUND);

				baseDao.saveObject(discount);
			} else if (DiscountCategory.isPoints(discount.getCategory()) && refundVo != null) {
				List<Long> usedItemIds = getUsedItemIds(discount.getUsedItems());
				if (CollectionUtils.isEmpty(usedItemIds)) {
					continue;
				}
				refundPoints(discount, refundVo, usedItemIds, seatDetails, detailMap);
			}
			result.add(discount);
		}

		return result;
	}

	private ResultCode memberPointReturn(Long memberId, OrderDiscount discount, String exserial, Integer pointValue) {
		MemberPointSpendVo spendVo = new MemberPointSpendVo();
		spendVo.setMemberId(memberId);
		spendVo.setSpendPoint(pointValue);
		spendVo.setTradeNo(discount.getTradeNo());
		spendVo.setSpendTime(new Timestamp(System.currentTimeMillis()));
		spendVo.setSpendType(discount.getOrderType());
		spendVo.setExserial(exserial);
		spendVo.setCompanyId(discount.getCompanyId());
//		spendVo.setRemark();
//		spendVo.setExclusivePointsId();
		return memberInfoApiService.memberPointReturn(spendVo);
	}

	/**
	 * 更新退票额度
	 *
	 * @param memberId
	 * @param companyId
	 * @param orderType
	 */
	private void updateRefundNum(Long memberId, Long companyId, String orderType, Integer refundQuantity, boolean refund, String serialno, List<OrderDiscountVo> orderDiscountVos,
								 ScheduleVo scheduleVo) {
		updateRestrictionRuleRefundNum(memberId, companyId, orderType, refundQuantity, refund);
		updateRefundProcessorRefundNum(memberId, companyId, orderType, refund, serialno, orderDiscountVos, scheduleVo);
	}

	private void updateRestrictionRuleRefundNum(Long memberId, Long companyId, String orderType, Integer refundQuantity, boolean refund) {
		MemberLevel level = memberInfoService.getActivatedLevel(memberId, companyId);
		if (level == null) {
			return;
		}
		String restrictionRuleIds = level.getRestrictionRuleIds();
		if (StringUtils.isBlank(restrictionRuleIds)) {
			return;
		}
		RestrictionRule rule = restrictionRuleService.getRule(BeanUtil.getIdList(restrictionRuleIds, ","), RestrictionRuleTypeConstant.RULE_TYPE_REFUND);
		if (rule == null) {
			return;
		}
		String key = RestrictionRuleTypeConstant.getKey(rule.getId(), memberId, companyId, level.getId());
		RestrictionRuleCheckVo checkVo = new RestrictionRuleCheckVo(rule.getLimitNum(), rule.getLimitcycle(), DateUtil.getCurFullTimestamp(), key, memberId, companyId, refundQuantity);
		if (StringUtils.isNotBlank(rule.getGroovyScript())) {
			checkVo.setGroovyScript(rule.getGroovyScript());
			checkVo.setGroovyContext(Map.of("orderType", orderType));
		}
		if (refund) {
			restrictionRuleService.updateRestrictionRule(checkVo);
		} else {
			restrictionRuleService.restoreRestrictionRule(checkVo);
		}
	}

	private void updateRefundProcessorRefundNum(Long memberId, Long companyId, String orderType, boolean refund, String serialno, List<OrderDiscountVo> orderDiscountVos, ScheduleVo scheduleVo) {
		if (refund) {
			Collection<RefundProcessorProvider> refundProcessorProviders = RefundProcessorRegister.getRefundProcessorProviders();
			if (CollectionUtils.isNotEmpty(refundProcessorProviders)) {
//				ResultCode<Map> refundCheckResult = ticketApiUntransService.getRefundStatus(companyId, scheduleVo.getId());
//				if(refundCheckResult.notSuccess()){
//					throw  refundCheckResult.toBizException();
//				}
//				String orderRange = MapUtils.getString(refundCheckResult.getData(), "orderRange");
//				if(!StringUtils.equals(CompanyRangeOrderRangeEnum.USE_TIMES_CARD.getCode(), orderRange)){
//					return;
//				}

				List<String> errMsgs = new ArrayList<>();
				Map<String, Object> otherParams = new HashMap<>(2);
				otherParams.put("orderDiscountVos", orderDiscountVos);
				otherParams.put("playEndTime", scheduleVo.getPlayTime());
				for (RefundProcessorProvider refundProcessorProvider : refundProcessorProviders) {
					ResultCode resultCode = refundProcessorProvider.updateLimitNum(serialno, orderType, memberId, companyId, otherParams);
					if (resultCode.isSuccess()) {
						return;
					}
					errMsgs.add(resultCode.getMsg());
				}
				if (CollectionUtils.isNotEmpty(errMsgs)) {
					throw new BizRTException(ResultCodeHelper.CODE11_DATA_NOT_ALLOWED, StringUtils.join(errMsgs, ";"));
				}
			}
		} else {
			Collection<RefundProcessorProvider> refundProcessorProviders = RefundProcessorRegister.getRefundProcessorProviders();
			if (CollectionUtils.isNotEmpty(refundProcessorProviders)) {
				for (RefundProcessorProvider refundProcessorProvider : refundProcessorProviders) {
					ResultCode resultCode = refundProcessorProvider.restoreLimitNum(serialno);
					if (resultCode.isSuccess()) {
						break;
					}
				}
			}
		}
	}

	@Override
	public void processRefundSuccess(OrderRefund refund, OrderRefundVo refundVo) {
		Assert.notNull(refund, "refund is need");
		if (refund.getStatus().equals(RefundConstant.STATUS_REFUND_SUCCESS)) {
			return;
		}
		String tradeNo = refund.getTradeNo();
		TicketOrder order = getTicketOrder(tradeNo);
		orderRefundService.successRefunded(refund, refundVo.getRefundtime());
		processTicketOrderRefundSuccess(order, refundVo);
	}

	@Override
	public ResultCode<OrderRefund> addOrderRefund(BaseOrder order, String otherinfo, MemberVo member, Integer refundQuantity, ScheduleVo scheduleVo) {
		// 新增申请记录
		ResultCode<OrderRefund> refundCode = orderRefundService.addOrderRefund(order, otherinfo, member);
		if (refundCode.notSuccess()) {
			return ResultCodeHelper.getFailure(refundCode);
		}
		// 更新退票额度
		String accessKey = companyService.getAccessKeyByAppkey(order.getAppkey());
		RefundReq refundCmd = JsonUtils.readJsonToObject(RefundReq.class, otherinfo);
		ResultCode<List<OrderDiscountVo>> resultCode = ticketOrderApiService.getOrderDiscounts(accessKey, order.getTradeNo(), refundCmd.getSeatLabels());
		if (resultCode.notSuccess()) {
			return refundCode;
		}
		updateRefundNum(order.getMemberId(), order.getCompanyId(), order.getOrderType(), refundQuantity, true, refundCode.getData().getSerialno(), resultCode.getData(), scheduleVo);
		return refundCode;
	}

	@Override
	public void rejectRefund(OrderRefund orderRefund, String cancelInfo, Long userid, Integer refundQuantity, ScheduleVo scheduleVo) {
		orderRefundService.rejectRefund(orderRefund, cancelInfo, userid); // close
		// 归还退票额度
		updateRefundNum(orderRefund.getMemberId(), orderRefund.getCompanyId(), orderRefund.getOrderType(), refundQuantity, false, orderRefund.getSerialno(), null, scheduleVo);
	}

	@Override
	public ResultCode updateOrderAddress(Long companyId, Long memberId, String tradeNo, String address) {
		TicketOrder ticketOrder = getTicketOrderByTradeNo(companyId, memberId, tradeNo);
		if (ticketOrder == null) {
			return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("订单不存在！");
		}
		if (StringUtils.isNotEmpty(address)) {
			ticketOrder.setTransport(TransportHelper.TAKE_EXPRESS);
			ticketOrder.setExpressAddress(address);
		} else {
			ticketOrder.setTransport(TransportHelper.CHECK_ETICKET);
			ticketOrder.setExpressAddress(null);
		}
		baseDao.saveObject(ticketOrder);
		ResultCode result = ticketOrderApiService.updateOrderAddress(companyId, memberId, tradeNo, address);
		if (result.notSuccess()) {
			throw result.toBizException();
		}
		return ResultCode.SUCCESS;
	}

	private TicketOrder getTicketOrderByTradeNo(Long companyId, Long memberId, String tradeNo) {
		DetachedCriteria query = DetachedCriteria.forClass(TicketOrder.class);
		query.add(Restrictions.eq("tradeNo", tradeNo));
		query.add(Restrictions.eq("companyId", companyId));
		query.add(Restrictions.eq("memberId", memberId));
		List<TicketOrder> orders = baseDao.findByCriteria(query, 0, 1);
		if (CollectionUtils.isEmpty(orders)) {
			return null;
		}
		return orders.get(0);
	}


	@Override
	public ResultCode<Integer> getValidTicketOrderTicketCount(Long companyId, Long memberId) {
		return ticketOrderApiService.getValidTicketOrderTicketCount(companyId, memberId);
	}

	@Override
	public List<TicketOrder> getListByTradeNos(Long companyId, Collection<String> tradeNos) {
		if (CollectionUtils.isEmpty(tradeNos)) {
			return new ArrayList(0);
		}
		DetachedCriteria query = DetachedCriteria.forClass(TicketOrder.class);
		query.add(Restrictions.eq("companyId", companyId));
		query.add(Restrictions.in("tradeNo", tradeNos));
		return baseDao.findByCriteria(query);
	}

	@Override
	public ResultCode updateOrderInvoiceflag(Long companyId, Long memberId, String tradeNo, String invoiceflag) {
		TicketOrder ticketOrder = getTicketOrderByTradeNo(companyId, memberId, tradeNo);
		if (ticketOrder == null) {
			return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("订单不存在！");
		}
		CheckOwnerUtil.checkCompanyOwner(companyId, ticketOrder);
		ticketOrder.setInvoiceflag(invoiceflag);
		ticketOrder.setUpdatetime(new Timestamp(System.currentTimeMillis()));
		baseDao.saveObject(ticketOrder);
		return ResultCode.SUCCESS;
	}

	@Override
	public void updateTicketOrderExpressFee(TicketOrder order, Double otherFee, String address) {
		Double oldOtherfee = order.getOtherFee();
		if (!ValueUtil.moneyEq(oldOtherfee, otherFee)) {
			order.setOtherFee(ValueUtil.round(otherFee));
			order.setTotalFee(ValueUtil.round(order.getTotalFee() + (otherFee - oldOtherfee)));
		}
		if (StringUtils.isNotEmpty(address)) {
			order.setTransport(TransportHelper.TAKE_EXPRESS);
			order.setExpressAddress(address);
		} else {
			order.setTransport(TransportHelper.CHECK_ETICKET);
			order.setExpressAddress(null);
		}
		order.setUpdatetime(DateUtil.getCurFullTimestamp());
		baseDao.updateObject(order);
	}

	@Override
	public ResultCode changeTicketCerts(ChangeOrderCertReq cmd) {
		TicketOrder order = getTicketOrder(cmd.getOutTradeNo());
		if (CollectionUtils.isNotEmpty(cmd.getDetails())) {
			List<SeatDetail> seats = baseDao.getObjectListByField(SeatDetail.class, "tradeNo", cmd.getOutTradeNo());
			Map<String, SeatDetail> seatMap = BeanUtil.beanListToMap(seats, "uuid");
			for (TicketCertDTO change : cmd.getDetails()) {
				SeatDetail detail = seatMap.get(change.getUuid());
				if (detail != null) {
					detail.setCertificateNo(IDCardUtil.getIDPartEncode(change.getCertificateStr()));
					detail.setRealname(change.getRealname());
					detail.setCertificateType(change.getCertificateType());
					baseDao.saveObject(detail);
				}
			}
		}
		if (StringUtils.isNotBlank(cmd.getCertificateStr())) {
			order.setCertificateNo(IDCardUtil.getIDPartEncode(cmd.getCertificateStr()));
			order.setContactName(cmd.getContactName());
			order.setCertificateType(cmd.getCertificateType());
			baseDao.saveObject(order);
		}
		return ResultCode.SUCCESS;
	}

	@Override
	public ResultCode bindTicketCert(TicketCertDTO change) {
		SeatDetail detail = baseDao.getObjectByUkey(SeatDetail.class, "uuid", change.getUuid());
		if (detail != null) {
			detail.setCertificateNo(IDCardUtil.getIDPartEncode(change.getCertificateStr()));
			detail.setRealname(change.getRealname());
			detail.setCertificateType(change.getCertificateType());
			baseDao.saveObject(detail);
			return ResultCode.SUCCESS;
		}
		return ResultCodeHelper.CODE11_DATA_NOT_EXISTS("数据不存在！");
	}

	@Override
	public TicketOrder updateTicketOrderContactName(MemberVo memberVo, String tradeNo, String contactName, String contactMobile) {
		TicketOrder ticketOrder = getTicketOrderByTradeNo(memberVo.getCompanyId(), memberVo.getId(), tradeNo);
		if (ticketOrder != null) {
			ticketOrder.setContactName(contactName);
			ticketOrder.setContactMobile(contactMobile);
			baseDao.saveObject(ticketOrder);
		}
		return ticketOrder;
	}

	@Override
	public ResultCode updateOrderSuccessOff(TicketOrder order, List<OrderTask> tasks) {
		Timestamp now = DateUtil.getCurFullTimestamp();
		order.setPayStatus(OrderStatus.STATUS_PAID_SUCCESS_OFF);
		order.setUpdatetime(now);
		baseDao.saveObject(order);
		if (CollectionUtils.isNotEmpty(tasks)) {
			for (OrderTask task : tasks) {
				task.setStatus(TaskConstant.STATUS_PASS);
				task.setUpdatetime(now);
			}
			baseDao.saveObjectList(tasks);
		}
		return ResultCode.SUCCESS;
	}
}
